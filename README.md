# 告警分发系统 (Alarm Distribution System)

本项目是一个灵活、可扩展的告警分发平台，旨在统一接收来自不同监控告警源（如 Prometheus AlertManager, Aliyun ARMS）的告警，并根据预设规则将其分发到指定的通知渠道（如飞书）。

系统的核心特性是支持基于告警内容（特别是 `region` 标签）的**区域路由**，能够将不同区域产生的告警精确地发送给对应区域的负责人或群组，实现高效的告警响应和管理。

## 核心功能

- **多源接入**: 支持多种主流监控系统的告警 webhook, 如 Prometheus AlertManager 和阿里云 ARMS。
- **多渠道通知**: 支持将告警发送到多种通知媒介，目前已内置飞书（Lark）。
- **基于区域的智能路由**: 可从告警标签中提取 `region` 信息，并将告警发送给该区域指定的飞书用户或群组。
- **灵活的路由规则**: 可根据告警的严重等级 (`severity`) 等字段定义分发规则。
- **统一数据模型**: 将不同来源的告警数据解析为统一的内部结构，便于处理和扩展。
- **配置驱动**: 所有路由规则、项目接入和通知渠道均通过 `config.yaml` 文件进行管理，无需修改代码。
- **���久化存储**: 使用 MongoDB 存储接收到的告警记录，便于后续审计和分析。

## 架构概览

系统的数据流如下：

```
+-------------------+      +------------------------+      +---------------------+      +-----------------------+      +--------------------+
|  监控系统         |----->|  告警分发系统          |----->|  告警解析器          |----->|  处理器与路由         |----->|  通知渠道 (飞书)    |
| (AlertManager,  |      |  (Webhook Endpoint)    |      | (Parser)            |      |  (Processor & Router) |      |  (Feishu Channel)  |
|  Aliyun ARMS)   |      |  /webhook/{project}    |      | (e.g., AliyunArms)  |      |  (Region-based)       |      |  (ou_xxxxxxxx)     |
+-------------------+      +------------------------+      +---------------------+      +-----------------------+      +--------------------+
```

## 快速开始

### 1. 先决条件

- **Go**: 版本 1.21 或更高。
- **MongoDB**: 一个正在运行的 MongoDB 实例。
- **飞书 (Lark)**: 一个飞书企业版账号，并已创建一个自定义应用以获取 `App ID` 和 `App Secret`。

### 2. 安装

1.  **克隆仓库**:
    ```bash
    git clone <your-repository-url>
    cd alarm_distribution
    ```

2.  **安装依赖**:
    ```bash
    go mod tidy
    ```

### 3. 配置

复制或重命名 `config.yaml` 文件，并根据您的环境填写以下字段。

```yaml
# config.yaml

server:
  host: "0.0.0.0"  # 监听的主机地址
  port: 8080         # 监听的端口

mongodb:
  uri: "mongodb://localhost:27017" # MongoDB 连接字符串
  database: "alarm_distribution"   # 数据库名称
  collection: "alerts"             # 集合名称

# 项目配置: 定义不同的告警源
projects:
  prometheus:
    name: "Prometheus AlertManager"
    type: "alertmanager" # 解析器类型
    endpoint: "/webhook/prometheus" # Webhook 路径
    channels: ["feishu"] # 默认通知渠道
    # ... 其他规则

  aliyun_arms:
    name: "Aliyun ARMS"
    type: "aliyun_arms"
    endpoint: "/webhook/aliyun_arms"
    channels: ["feishu"]
    # ... 其他规则

# 通知渠道配置
notifications:
  feishu:
    app_id: "YOUR_APP_ID"         # 飞书应用的 App ID
    app_secret: "YOUR_APP_SECRET" # 飞书应用的 App Secret
    enabled: true
    # 默认接收者 (当区域不匹配时)
    default_receiver: "ou_xxxxxxxxxxxxxxxxxxxxxxxx" 
    # 基于区域的接收者映射 (key: region, value: 飞书 open_id)
    region_receivers:
      "cn-hangzhou": "ou_xxxxxxxx_cn_hangzhou"
      "cn-shanghai": "ou_xxxxxxxx_cn_shanghai"
      "ap-southeast-1": "ou_xxxxxxxx_ap_southeast_1"
      "us-west-1": "ou_xxxxxxxx_us_west_1"
```

**关键配置说明**:

-   `projects`:
    -   `type`: 必须与 `internal/parsers` 中的解析器类型匹配 (`alertmanager` 或 `aliyun_arms`)。
    -   `endpoint`: 定义了此项目接收告警的 HTTP 路径。
-   `notifications.feishu`:
    -   `app_id` / `app_secret`: 必须填写您的飞书自定义应用凭证。
    -   `default_receiver`: 当告警中没有 `region` 标签，或 `region` 标签没有在 `region_receivers` 中匹配到时，使用的默认飞书用户 `open_id`。
    -   `region_receivers`: **核心功能**。这里的 `key` 是从告警标签中提取的区域标识 (如 `_aliyun_arms_region_id`, `region`), `value` 是对应区域负责人的飞书 `open_id`。

### 4. 运行服务

```bash
go run cmd/server/main.go
```

服务启动后，将开始在 `config.yaml` 中配置的 `host:port` 上监听告警请求。

## API 使用

将您的监控系统 (如 AlertManager 或 Aliyun ARMS) 的 webhook 地址配置为本服务的端点。

**端点格式**: `http://<your_server_ip>:<port><project_endpoint>`

例如，对于 `config.yaml` 中定义的 `aliyun_arms` 项目，webhook URL 为:
`http://127.0.0.1:8080/webhook/aliyun_arms`

### cURL 测试示例

您可以使用 `curl` 来模拟一个来自 Aliyun ARMS 的告警请求：

```bash
curl -X POST http://127.0.0.1:8080/webhook/aliyun_arms \
-H 'Content-Type: application/json' \
-d '{
  "status": "firing",
  "receiver": "webhook-receiver",
  "groupLabels": {
    "alertname": "CPUUsageTooHigh"
  },
  "commonLabels": {
    "alertname": "CPUUsageTooHigh",
    "severity": "critical",
    "_aliyun_arms_region_id": "cn-hangzhou"
  },
  "commonAnnotations": {
    "message": "The CPU usage is over 80% for 5 minutes."
  },
  "externalURL": "http://aliyun.com/arms",
  "alerts": [
    {
      "status": "firing",
      "labels": {
        "alertname": "CPUUsageTooHigh",
        "severity": "critical",
        "_aliyun_arms_region_id": "cn-hangzhou",
        "instance": "host-123"
      },
      "annotations": {
        "message": "The CPU usage is over 80% for 5 minutes on host-123."
      },
      "startsAt": "2025-07-18T10:00:00Z",
      "endsAt": "0001-01-01T00:00:00Z",
      "fingerprint": "a1b2c3d4e5f6"
    }
  ]
}'
```

根据配置，这个来自 `cn-hangzhou` 区域的告警将会被发送到 `region_receivers` 中 `cn-hangzhou` 对应的飞书用户。

## 项目结构

```
.
├── cmd/server/main.go      # 程序入口
├── config.yaml             # 配置文件
├── internal/
│   ├── config/             # 配置加载
│   ├── handlers/           # HTTP Webhook 处理器
│   ├── models/             # 统一的数据模型 (Alert, AlertGroup等)
│   ├── notifications/      # 通知渠道实现 (如 feishu.go)
│   ├── parsers/            # 不同告警源的解析器
│   ├── processor/          # 告警处理和路由逻辑
│   ├── server/             # HTTP 服务封装
│   └── storage/            # 数据存储 (MongoDB)
├── go.mod
└── README.md
```

## 许可证

本项目采用 [MIT License](LICENSE) 许可证。
