server:
  host: "0.0.0.0"
  port: 8080

mongodb:
  uri: "mongodb://root:<EMAIL>:3717,dds-wz9fcffa25655f942.mongodb.rds.aliyuncs.com:3717/admin?replicaSet=mgset-89747277"
  database: "alarm_distribution"
  collection: "alerts"

projects:
  prometheus:
    name: "Prometheus AlertManager"
    type: "alertmanager"
    endpoint: "/webhook/prometheus"
    channels: ["feishu"]
    severity_map:
      critical: "ERROR"
      warning: "WARN"
      info: "INFO"
    rules:
      - severity: "ERROR"
        channels: ["feishu"]
      - severity: "WARN"
        channels: ["feishu"]
      - severity: "INFO"
        channels: ["feishu"]

  aliyun_arms:
    name: "Aliyun ARMS"
    type: "aliyun_arms"
    endpoint: "/webhook/aliyun_arms"
    channels: ["feishu"]
    severity_map:
      ERROR: "ERROR"
      WARN: "WARN"
      INFO: "INFO"
    rules:
      - severity: "ERROR"
        channels: ["feishu"]
      - severity: "WARN"
        channels: ["feishu"]

notifications:
  feishu:
    app_id: "cli_a8a01ad9653ed00b"
    app_secret: "F2Z5lVDDdRaYocN7EbQyZfXDM3WyBqdd"
    enabled: true
    default_receiver: "oc_ac433e5108cbb8661ce7359f55f787a8" # Default receiver for regions not configured
    # region_receivers:
      # "cn-hangzhou": "ou_demo_cn_hangzhou"
      # "cn-shanghai": "ou_demo_cn_shanghai"
      # "cn-beijing": "ou_demo_cn_beijing"
      # "cn-shenzhen": "ou_demo_cn_shenzhen"
      # "ap-southeast-1": "ou_demo_sg"
      # "ap-east-1": "ou_demo_hk"
      # "us-west-1": "ou_demo_us_west"
      # "us-east-1": "ou_demo_us_east"
      # "eu-central-1": "ou_demo_eu"
