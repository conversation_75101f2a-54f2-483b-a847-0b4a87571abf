# 基于地域的飞书群路由功能

## 概述

该功能允许根据告警消息中的地域信息，将告警发送到不同的飞书群。这对于多地域部署的系统特别有用，可以确保每个地域的告警发送到对应地域的运维团队。

## 功能特性

- 支持阿里云ARMS告警中的 `_aliyun_arms_region_id` 字段
- 支持通用的地域标签（`region`, `aws_region`, `gcp_region`）
- 提供默认接收者配置，用于未配置地域或无地域信息的告警
- 灵活的地域到飞书群的映射配置

## 配置说明

### 飞书配置结构

在 `config.yaml` 文件中，飞书配置现在支持以下字段：

```yaml
notifications:
  feishu:
    app_id: "YOUR_APP_ID"
    app_secret: "YOUR_APP_SECRET"
    enabled: true
    default_receiver: "ou_7d8a6e6df7621556ce0d21922b676706ccs"  # 默认接收者ID
    region_receivers:
      # 阿里云地域
      "cn-hangzhou": "ou_demo_cn_hangzhou"
      "cn-shanghai": "ou_demo_cn_shanghai"
      "cn-beijing": "ou_demo_cn_beijing"
      "cn-shenzhen": "ou_demo_cn_shenzhen"
      "ap-southeast-1": "ou_demo_sg"  # 新加坡
      "ap-southeast-3": "ou_demo_sg"  # 新加坡（备用）
      "ap-east-1": "ou_demo_hk"       # 香港
      "us-west-1": "ou_demo_us_west"
      "us-east-1": "ou_demo_us_east"
      "eu-central-1": "ou_demo_eu"
```

### 配置字段说明

- `default_receiver`: 默认的飞书群接收者ID，用于以下情况：
  - 告警中没有地域信息
  - 告警中的地域信息在 `region_receivers` 中未配置
  - `region_receivers` 配置为空或未配置

- `region_receivers`: 地域到飞书群接收者ID的映射表
  - 键：地域标识符（如 `cn-hangzhou`, `ap-southeast-1` 等）
  - 值：对应的飞书群接收者ID

## 地域信息提取规则

系统按以下优先级顺序提取告警中的地域信息：

1. `_aliyun_arms_region_id` - 阿里云ARMS专用地域字段
2. `region` - 通用地域字段
3. `aws_region` - AWS地域字段
4. `gcp_region` - GCP地域字段

## 使用示例

### 阿里云ARMS告警示例

当收到如下阿里云ARMS告警时：

```json
{
  "alerts": [
    {
      "labels": {
        "_aliyun_arms_region_id": "cn-hangzhou",
        "alertname": "容器内存使用率大于80%",
        "severity": "warning"
      }
    }
  ]
}
```

系统会：
1. 提取地域信息：`cn-hangzhou`
2. 查找配置中的 `region_receivers["cn-hangzhou"]`
3. 使用对应的接收者ID `ou_demo_cn_hangzhou` 发送告警

### 无地域信息的告警

对于没有地域信息的告警，系统会使用 `default_receiver` 配置的接收者ID。

## 测试

项目包含了完整的单元测试来验证地域路由功能：

```bash
# 运行飞书通知相关测试
go test ./internal/notifications -v

# 运行所有测试
go test ./... -v
```

## 部署注意事项

1. **接收者ID获取**: 需要从飞书管理后台获取正确的群组接收者ID
2. **地域标识符**: 确保配置中的地域标识符与实际告警中的地域字段值完全匹配
3. **默认接收者**: 建议始终配置 `default_receiver`，确保所有告警都能被正确路由
4. **配置验证**: 部署前建议通过测试验证配置的正确性

## 兼容性

- 该功能向后兼容，不会影响现有的告警处理流程
- 如果不配置 `region_receivers`，系统会使用 `default_receiver`
- 支持多种告警源（AlertManager、阿里云ARMS等）

## 故障排除

### 常见问题

1. **告警发送到错误的群组**
   - 检查地域标识符是否正确匹配
   - 验证接收者ID是否正确

2. **告警发送到默认群组**
   - 检查告警中是否包含地域信息
   - 验证地域信息是否在 `region_receivers` 中配置

3. **告警发送失败**
   - 检查接收者ID是否有效
   - 验证飞书应用权限配置

### 日志信息

系统会在日志中记录发送的接收者ID，便于调试：

```
Successfully sent Feishu notification for alert: 容器内存使用率大于80% to receiver: ou_demo_cn_hangzhou
```
