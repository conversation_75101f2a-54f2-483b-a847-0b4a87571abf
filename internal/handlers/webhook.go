package handlers

import (
	"context"
	"log"
	"time"

	"github.com/valyala/fasthttp"

	"alarm_distribution/internal/models"
	"alarm_distribution/internal/processor"
)

// WebhookHandler handles webhook requests
type WebhookHandler struct {
	processor *processor.AlertProcessor
	parser    models.AlertParser
}

// NewWebhookHandler creates a new webhook handler
func NewWebhookHandler(processor *processor.AlertProcessor, parser models.AlertParser) *WebhookHandler {
	return &WebhookHandler{
		processor: processor,
		parser:    parser,
	}
}

// <PERSON><PERSON> processes webhook requests
func (h *WebhookHandler) Handle(ctx *fasthttp.RequestCtx, projectName string) {
	// Validate request method
	if !ctx.IsPost() {
		ctx.SetStatusCode(fasthttp.StatusMethodNotAllowed)
		ctx.SetBodyString("Method not allowed")
		return
	}

	// Validate content type
	contentType := string(ctx.Request.Header.ContentType())
	if contentType != "application/json" {
		log.Printf("Invalid content type: %s", contentType)
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		ctx.SetBodyString("Content-Type must be application/json")
		return
	}

	// Get request body
	body := ctx.PostBody()
	if len(body) == 0 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		ctx.SetBodyString("Empty request body")
		return
	}

	// Parse the alert data
	alertGroup, err := h.parser.Parse(body, projectName)
	if err != nil {
		log.Printf("Failed to parse alert data for project %s: %v", projectName, err)
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		ctx.SetBodyString("Failed to parse alert data")
		return
	}

	// Process the alert group
	processCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := h.processor.ProcessAlertGroup(processCtx, alertGroup); err != nil {
		log.Printf("Failed to process alert group for project %s: %v", projectName, err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		ctx.SetBodyString("Failed to process alerts")
		return
	}

	// Return success response
	ctx.SetStatusCode(fasthttp.StatusOK)
	ctx.SetContentType("application/json")
	ctx.SetBodyString(`{"status":"success","message":"Alerts processed successfully"}`)
}
