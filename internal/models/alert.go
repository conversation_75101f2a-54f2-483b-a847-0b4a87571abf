package models

import (
	"time"
)

// AlertStatus represents the status of an alert
type AlertStatus string

const (
	AlertStatusFiring   AlertStatus = "firing"
	AlertStatusResolved AlertStatus = "resolved"
)

// AlertSeverity represents the severity level of an alert
type AlertSeverity string

const (
	AlertSeverityError   AlertSeverity = "ERROR"
	AlertSeverityWarning AlertSeverity = "WARN"
	AlertSeverityInfo    AlertSeverity = "INFO"
)

// Alert represents a normalized alert structure
type Alert struct {
	ID          string            `json:"id" bson:"_id"`
	Fingerprint string            `json:"fingerprint" bson:"fingerprint"`
	ProjectName string            `json:"project_name" bson:"project_name"`
	ProjectType string            `json:"project_type" bson:"project_type"`
	Status      AlertStatus       `json:"status" bson:"status"`
	Severity    AlertSeverity     `json:"severity" bson:"severity"`
	AlertName   string            `json:"alert_name" bson:"alert_name"`
	Message     string            `json:"message" bson:"message"`
	Labels      map[string]string `json:"labels" bson:"labels"`
	Annotations map[string]string `json:"annotations" bson:"annotations"`
	StartsAt    time.Time         `json:"starts_at" bson:"starts_at"`
	EndsAt      *time.Time        `json:"ends_at,omitempty" bson:"ends_at,omitempty"`
	CreatedAt   time.Time         `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at" bson:"updated_at"`
	RawData     map[string]any    `json:"raw_data" bson:"raw_data"`
}

// AlertGroup represents a group of alerts (like from AlertManager)
type AlertGroup struct {
	Receiver          string            `json:"receiver"`
	Status            AlertStatus       `json:"status"`
	Alerts            []Alert           `json:"alerts"`
	GroupLabels       map[string]string `json:"group_labels"`
	CommonLabels      map[string]string `json:"common_labels"`
	CommonAnnotations map[string]string `json:"common_annotations"`
	ExternalURL       string            `json:"external_url"`
	RawData           map[string]any    `json:"raw_data"`
}

// AlertParser defines the interface for parsing different alert formats
type AlertParser interface {
	Parse(data []byte, projectName string) (*AlertGroup, error)
	GetProjectType() string
}

// NotificationMessage represents a message to be sent via notification channels
type NotificationMessage struct {
	Alert     *Alert        `json:"alert"`
	Title     string        `json:"title"`
	Content   string        `json:"content"`
	Severity  AlertSeverity `json:"severity"`
	Timestamp time.Time     `json:"timestamp"`
}

// NotificationChannel defines the interface for notification channels
type NotificationChannel interface {
	Send(message *NotificationMessage) error
	GetChannelType() string
}
