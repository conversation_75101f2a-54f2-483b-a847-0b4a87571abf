package notifications

import (
	"fmt"
	"time"

	"alarm_distribution/internal/models"
)

// MessageFormatter provides common message formatting functionality
type MessageFormatter struct{}

// FormatAlertMessage formats an alert into a notification message
func (f *MessageFormatter) FormatAlertMessage(alert *models.Alert) *models.NotificationMessage {
	title := f.formatTitle(alert)
	content := f.formatContent(alert)

	return &models.NotificationMessage{
		Alert:     alert,
		Title:     title,
		Content:   content,
		Severity:  alert.Severity,
		Timestamp: time.Now(),
	}
}

// formatTitle creates a title for the notification
func (f *MessageFormatter) formatTitle(alert *models.Alert) string {
	statusEmoji := "🔥"
	if alert.Status == models.AlertStatusResolved {
		statusEmoji = "✅"
	}

	severityEmoji := f.getSeverityEmoji(alert.Severity)

	return fmt.Sprintf("%s %s [%s] %s",
		statusEmoji,
		severityEmoji,
		string(alert.Status),
		alert.AlertName)
}

// formatContent creates detailed content for the notification
func (f *MessageFormatter) formatContent(alert *models.Alert) string {
	// content := fmt.Sprintf("**Alert Details:**\n")
	content := "**Alert Details:**\n"
	content += fmt.Sprintf("• **Name:** %s\n", alert.AlertName)
	content += fmt.Sprintf("• **Status:** %s\n", string(alert.Status))
	content += fmt.Sprintf("• **Severity:** %s\n", string(alert.Severity))
	content += fmt.Sprintf("• **Project:** %s\n", alert.ProjectName)
	content += fmt.Sprintf("• **Message:** %s\n", alert.Message)
	content += fmt.Sprintf("• **Started At:** %s\n", alert.StartsAt.Format("2006-01-02 15:04:05"))

	if alert.EndsAt != nil {
		content += fmt.Sprintf("• **Ended At:** %s\n", alert.EndsAt.Format("2006-01-02 15:04:05"))
	}

	// Add important labels
	if len(alert.Labels) > 0 {
		content += "\n**Labels:**\n"
		for key, value := range alert.Labels {
			// Only show important labels to avoid clutter
			if f.isImportantLabel(key) {
				content += fmt.Sprintf("• **%s:** %s\n", key, value)
			}
		}
	}

	return content
}

// getSeverityEmoji returns an emoji for the severity level
func (f *MessageFormatter) getSeverityEmoji(severity models.AlertSeverity) string {
	switch severity {
	case models.AlertSeverityError:
		return "🚨"
	case models.AlertSeverityWarning:
		return "⚠️"
	case models.AlertSeverityInfo:
		return "ℹ️"
	default:
		return "📢"
	}
}

// isImportantLabel determines if a label should be included in the notification
func (f *MessageFormatter) isImportantLabel(key string) bool {
	importantLabels := map[string]bool{
		"instance":    true,
		"job":         true,
		"service":     true,
		"namespace":   true,
		"pod":         true,
		"container":   true,
		"node":        true,
		"cluster":     true,
		"environment": true,
		"region":      true,
	}

	return importantLabels[key]
}
