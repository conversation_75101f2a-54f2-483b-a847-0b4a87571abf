package notifications

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"

	"alarm_distribution/internal/config"
	"alarm_distribution/internal/models"
)

// FeishuChannel implements notification channel for Feishu (Lark)
type FeishuChannel struct {
	client    *lark.Client
	formatter *MessageFormatter
	config    *config.FeishuConfig
}

// NewFeishuChannel creates a new Feishu notification channel
func NewFeishuChannel(cfg *config.FeishuConfig) *FeishuChannel {
	client := lark.NewClient(cfg.AppID, cfg.AppSecret, lark.WithEnableTokenCache(true))

	return &FeishuChannel{
		client:    client,
		formatter: &MessageFormatter{},
		config:    cfg,
	}
}

// Send sends a notification message via Feishu
func (f *FeishuChannel) Send(message *models.NotificationMessage) error {
	// Format message for Feishu
	content := f.formatFeishuMessage(message)

	// Determine receiver ID based on region
	receiverID := f.getReceiverIDByRegion(message.Alert)

	// Create message request
	req := larkim.NewCreateMessageReqBuilder().
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(receiverID).
			MsgType(`interactive`).
			Content(content).
			Build()).
		Build()

	// Send message
	resp, err := f.client.Im.V1.Message.Create(context.Background(), req)
	if err != nil {
		return fmt.Errorf("failed to send Feishu message: %w", err)
	}

	// Check response
	if !resp.Success() {
		return fmt.Errorf("feishu API error: %s", resp.CodeError.Msg)
	}

	log.Printf("Successfully sent Feishu notification for alert: %s to receiver: %s", message.Alert.AlertName, receiverID)
	return nil
}

// GetChannelType returns the channel type
func (f *FeishuChannel) GetChannelType() string {
	return "feishu"
}

// getReceiverIDByRegion determines the receiver ID based on the alert's region
func (f *FeishuChannel) getReceiverIDByRegion(alert *models.Alert) string {
	// Extract region from alert labels
	regionID := f.extractRegionFromAlert(alert)

	// If no region found or no region receivers configured, use default
	if regionID == "" || f.config.RegionReceivers == nil {
		return f.config.DefaultReceiver
	}

	// Look for region-specific receiver
	if receiverID, exists := f.config.RegionReceivers[regionID]; exists {
		return receiverID
	}

	// Fallback to default receiver
	return f.config.DefaultReceiver
}

// extractRegionFromAlert extracts region information from alert labels
func (f *FeishuChannel) extractRegionFromAlert(alert *models.Alert) string {
	// For Aliyun ARMS alerts, check for _aliyun_arms_region_id
	if regionID, exists := alert.Labels["_aliyun_arms_region_id"]; exists {
		return regionID
	}

	// No region found
	return ""
}

// formatFeishuMessage formats the message for Feishu interactive card
func (f *FeishuChannel) formatFeishuMessage(message *models.NotificationMessage) string {
	alert := message.Alert

	// Build content for the markdown element
	var elements []string
	elements = append(elements, fmt.Sprintf("**Status:** %s", string(alert.Status)))
	elements = append(elements, fmt.Sprintf("**Severity:** %s", string(alert.Severity)))
	elements = append(elements, fmt.Sprintf("**Project:** %s", alert.ProjectName))
	elements = append(elements, fmt.Sprintf("**Started:** %s", alert.StartsAt.Format("2006-01-02 15:04:05")))
	if alert.EndsAt != nil {
		elements = append(elements, fmt.Sprintf("**Ended:** %s", alert.EndsAt.Format("2006-01-02 15:04:05")))
	}

	markdownContent := strings.Join(elements, "\n")
	markdownContent += fmt.Sprintf("\n\n**Message:**\n%s", alert.Message)

	// Add important labels
	var labelElements []string
	if len(alert.Labels) > 0 {
		for key, value := range alert.Labels {
			if f.formatter.isImportantLabel(key) {
				labelElements = append(labelElements, fmt.Sprintf("**%s:** %s", key, value))
			}
		}
	}
	if len(labelElements) > 0 {
		markdownContent += "\n\n**Labels:**\n" + strings.Join(labelElements, "\n")
	}

	// Create interactive card using v2.0 schema
	card := map[string]any{
		"schema": "2.0",
		"config": map[string]any{
			"width_mode": "fill",
		},
		"header": map[string]any{
			"title": map[string]any{
				"tag":     "plain_text",
				"content": message.Title,
			},
			"template": f.getHeaderTemplate(alert.Severity),
		},
		"body": map[string]any{
			"elements": []any{
				map[string]any{
					"tag":     "markdown",
					"content": markdownContent,
				},
			},
		},
	}

	// Convert to JSON string
	cardJSON, err := json.Marshal(card)
	if err != nil {
		log.Printf("Failed to marshal Feishu card: %v", err)
		// Fallback to simple text message
		return fmt.Sprintf(`{"text":"%s"}`, message.Content)
	}

	return string(cardJSON)
}

// getHeaderTemplate returns the appropriate header template based on severity
func (f *FeishuChannel) getHeaderTemplate(severity models.AlertSeverity) string {
	switch severity {
	case models.AlertSeverityError:
		return "red"
	case models.AlertSeverityWarning:
		return "orange"
	case models.AlertSeverityInfo:
		return "blue"
	default:
		return "green"
	}
}
