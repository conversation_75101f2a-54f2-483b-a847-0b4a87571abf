package parsers

import (
	"encoding/json"
	"fmt"
	"time"

	"alarm_distribution/internal/models"
)

// AlertManagerCommonParser implements the AlertParser interface for standard AlertManager format
type AlertManagerCommonParser struct{}

// AlertManagerCommonWebhook represents the standard AlertManager webhook payload structure
// Based on the official AlertManager webhook format specification
type AlertManagerCommonWebhook struct {
	Version           string                    `json:"version"`           // AlertManager version
	GroupKey          string                    `json:"groupKey"`          // Key identifying the group of alerts
	TruncatedAlerts   int                       `json:"truncatedAlerts"`   // Number of alerts truncated due to max_alerts
	Status            string                    `json:"status"`            // resolved|firing
	Receiver          string                    `json:"receiver"`          // Receiver name
	GroupLabels       map[string]string         `json:"groupLabels"`       // Labels used for grouping
	CommonLabels      map[string]string         `json:"commonLabels"`      // Labels common to all alerts
	CommonAnnotations map[string]string         `json:"commonAnnotations"` // Annotations common to all alerts
	ExternalURL       string                    `json:"externalURL"`       // Backlink to the Alertmanager
	Alerts            []AlertManagerCommonAlert `json:"alerts"`            // List of alerts
}

// AlertManagerCommonAlert represents a single alert from standard AlertManager
type AlertManagerCommonAlert struct {
	Status       string            `json:"status"`       // resolved|firing
	Labels       map[string]string `json:"labels"`       // Alert labels
	Annotations  map[string]string `json:"annotations"`  // Alert annotations
	StartsAt     time.Time         `json:"startsAt"`     // Alert start time (RFC3339)
	EndsAt       time.Time         `json:"endsAt"`       // Alert end time (RFC3339)
	GeneratorURL string            `json:"generatorURL"` // URL identifying the entity that caused the alert
	Fingerprint  string            `json:"fingerprint"`  // Fingerprint to identify the alert
}

// NewAlertManagerCommonParser creates a new standard AlertManager parser
func NewAlertManagerCommonParser() *AlertManagerCommonParser {
	return &AlertManagerCommonParser{}
}

// Parse parses standard AlertManager webhook data into normalized Alert structure
func (p *AlertManagerCommonParser) Parse(data []byte, projectName string) (*models.AlertGroup, error) {
	var webhook AlertManagerCommonWebhook
	if err := json.Unmarshal(data, &webhook); err != nil {
		return nil, fmt.Errorf("failed to unmarshal AlertManager webhook: %w", err)
	}

	// Convert to normalized alert group
	alertGroup := &models.AlertGroup{
		Receiver:          webhook.Receiver,
		Status:            models.AlertStatus(webhook.Status),
		GroupLabels:       webhook.GroupLabels,
		CommonLabels:      webhook.CommonLabels,
		CommonAnnotations: webhook.CommonAnnotations,
		ExternalURL:       webhook.ExternalURL,
		RawData:           make(map[string]any),
	}

	// Store raw data
	rawData := make(map[string]any)
	json.Unmarshal(data, &rawData)
	alertGroup.RawData = rawData

	// Convert individual alerts
	for _, amAlert := range webhook.Alerts {
		alert := models.Alert{
			ID:          generateAlertManagerID(amAlert.Fingerprint, projectName),
			Fingerprint: amAlert.Fingerprint,
			ProjectName: projectName,
			ProjectType: p.GetProjectType(),
			Status:      models.AlertStatus(amAlert.Status),
			Severity:    p.extractSeverity(amAlert.Labels),
			AlertName:   p.extractAlertName(amAlert.Labels),
			Message:     p.extractMessage(amAlert.Annotations),
			Labels:      amAlert.Labels,
			Annotations: amAlert.Annotations,
			StartsAt:    amAlert.StartsAt,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
			RawData:     make(map[string]any),
		}

		// Set EndsAt if alert is resolved
		if amAlert.Status == "resolved" && !amAlert.EndsAt.IsZero() {
			alert.EndsAt = &amAlert.EndsAt
		}

		// Store raw alert data
		alertRawData := make(map[string]any)
		alertBytes, _ := json.Marshal(amAlert)
		json.Unmarshal(alertBytes, &alertRawData)
		alert.RawData = alertRawData

		alertGroup.Alerts = append(alertGroup.Alerts, alert)
	}

	return alertGroup, nil
}

// GetProjectType returns the project type for standard AlertManager
func (p *AlertManagerCommonParser) GetProjectType() string {
	return "alertmanager"
}

// extractSeverity extracts severity from alert labels
func (p *AlertManagerCommonParser) extractSeverity(labels map[string]string) models.AlertSeverity {
	if severity, exists := labels["severity"]; exists {
		switch severity {
		case "critical":
			return models.AlertSeverityError
		case "warning":
			return models.AlertSeverityWarning
		case "info":
			return models.AlertSeverityInfo
		default:
			return models.AlertSeverityInfo
		}
	}
	return models.AlertSeverityInfo
}

// extractAlertName extracts alert name from labels
func (p *AlertManagerCommonParser) extractAlertName(labels map[string]string) string {
	if alertname, exists := labels["alertname"]; exists {
		return alertname
	}
	return "Unknown Alert"
}

// extractMessage extracts message from annotations
func (p *AlertManagerCommonParser) extractMessage(annotations map[string]string) string {
	if summary, exists := annotations["summary"]; exists {
		return summary
	}
	if description, exists := annotations["description"]; exists {
		return description
	}
	if message, exists := annotations["message"]; exists {
		return message
	}
	return "No message available"
}

// generateAlertManagerID generates a unique ID for the AlertManager alert
func generateAlertManagerID(fingerprint, projectName string) string {
	return fmt.Sprintf("%s_%s_%d", projectName, fingerprint, time.Now().Unix())
}
