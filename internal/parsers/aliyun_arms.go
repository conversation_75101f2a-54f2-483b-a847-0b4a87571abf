package parsers

import (
	"encoding/json"
	"fmt"
	"time"

	"alarm_distribution/internal/models"
)

// AliyunArmsParser implements the AlertParser interface for Aliyun ARMS format
type AliyunArmsParser struct{}

// AliyunArmsWebhook represents the Aliyun ARMS webhook payload structure
// Based on Aliyun ARMS alert format, which is similar to AlertManager but with additional fields
type AliyunArmsWebhook struct {
	Status            string            `json:"status"`            // resolved|firing
	Receiver          string            `json:"receiver"`          // Receiver name
	GroupLabels       map[string]string `json:"groupLabels"`       // Labels used for grouping
	CommonLabels      map[string]string `json:"commonLabels"`      // Labels common to all alerts
	CommonAnnotations map[string]string `json:"commonAnnotations"` // Annotations common to all alerts
	ExternalURL       string            `json:"externalURL"`       // Backlink to the Aliyun ARMS console
	Alerts            []AliyunArmsAlert `json:"alerts"`            // List of alerts
}

// AliyunArmsAlert represents a single alert from Aliyun ARMS
type AliyunArmsAlert struct {
	Status      string            `json:"status"`      // resolved|firing
	Labels      map[string]string `json:"labels"`      // Alert labels (includes ARMS-specific labels)
	Annotations map[string]string `json:"annotations"` // Alert annotations (includes ARMS-specific annotations)
	StartsAt    time.Time         `json:"startsAt"`    // Alert start time (RFC3339)
	EndsAt      time.Time         `json:"endsAt"`      // Alert end time (RFC3339)
	Fingerprint string            `json:"fingerprint"` // Fingerprint to identify the alert
}

// NewAliyunArmsParser creates a new Aliyun ARMS parser
func NewAliyunArmsParser() *AliyunArmsParser {
	return &AliyunArmsParser{}
}

// Parse parses Aliyun ARMS webhook data into normalized Alert structure
func (p *AliyunArmsParser) Parse(data []byte, projectName string) (*models.AlertGroup, error) {
	var webhook AliyunArmsWebhook
	if err := json.Unmarshal(data, &webhook); err != nil {
		return nil, fmt.Errorf("failed to unmarshal Aliyun ARMS webhook: %w", err)
	}

	// Convert to normalized alert group
	alertGroup := &models.AlertGroup{
		Receiver:          webhook.Receiver,
		Status:            models.AlertStatus(webhook.Status),
		GroupLabels:       webhook.GroupLabels,
		CommonLabels:      webhook.CommonLabels,
		CommonAnnotations: webhook.CommonAnnotations,
		ExternalURL:       webhook.ExternalURL,
		RawData:           make(map[string]any),
	}

	// Store raw data
	rawData := make(map[string]any)
	json.Unmarshal(data, &rawData)
	alertGroup.RawData = rawData

	// Convert individual alerts
	for _, armsAlert := range webhook.Alerts {
		alert := models.Alert{
			ID:          generateAliyunArmsID(armsAlert.Fingerprint, projectName),
			Fingerprint: armsAlert.Fingerprint,
			ProjectName: projectName,
			ProjectType: p.GetProjectType(),
			Status:      models.AlertStatus(armsAlert.Status),
			Severity:    p.extractSeverity(armsAlert.Labels),
			AlertName:   p.extractAlertName(armsAlert.Labels),
			Message:     p.extractMessage(armsAlert.Annotations),
			Labels:      armsAlert.Labels,
			Annotations: armsAlert.Annotations,
			StartsAt:    armsAlert.StartsAt,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
			RawData:     make(map[string]any),
		}

		// Set EndsAt if alert is resolved
		if armsAlert.Status == "resolved" && !armsAlert.EndsAt.IsZero() {
			alert.EndsAt = &armsAlert.EndsAt
		}

		// Store raw alert data
		alertRawData := make(map[string]any)
		alertBytes, _ := json.Marshal(armsAlert)
		json.Unmarshal(alertBytes, &alertRawData)
		alert.RawData = alertRawData

		alertGroup.Alerts = append(alertGroup.Alerts, alert)
	}

	return alertGroup, nil
}

// GetProjectType returns the project type for Aliyun ARMS
func (p *AliyunArmsParser) GetProjectType() string {
	return "aliyun_arms"
}

// extractSeverity extracts severity from alert labels
// Aliyun ARMS uses _aliyun_arms_alert_level field for severity
func (p *AliyunArmsParser) extractSeverity(labels map[string]string) models.AlertSeverity {
	// First check for ARMS-specific alert level
	if alertLevel, exists := labels["_aliyun_arms_alert_level"]; exists {
		switch alertLevel {
		case "ERROR":
			return models.AlertSeverityError
		case "WARN":
			return models.AlertSeverityWarning
		case "INFO":
			return models.AlertSeverityInfo
		default:
			return models.AlertSeverityInfo
		}
	}

	// Fallback to standard severity field
	if severity, exists := labels["severity"]; exists {
		switch severity {
		case "critical":
			return models.AlertSeverityError
		case "warning":
			return models.AlertSeverityWarning
		case "info":
			return models.AlertSeverityInfo
		default:
			return models.AlertSeverityInfo
		}
	}

	return models.AlertSeverityInfo
}

// extractAlertName extracts alert name from labels
func (p *AliyunArmsParser) extractAlertName(labels map[string]string) string {
	if alertname, exists := labels["alertname"]; exists {
		return alertname
	}
	return "Unknown Alert"
}

// extractMessage extracts message from annotations
// Aliyun ARMS has specific message fields
func (p *AliyunArmsParser) extractMessage(annotations map[string]string) string {
	// Check for ARMS-specific message field first
	if armsMessage, exists := annotations["_aliyun_arms_alert_message"]; exists && armsMessage != "null" {
		return armsMessage
	}

	// Check for standard message field
	if message, exists := annotations["message"]; exists {
		return message
	}

	// Fallback to other common fields
	if summary, exists := annotations["summary"]; exists {
		return summary
	}
	if description, exists := annotations["description"]; exists {
		return description
	}

	return "No message available"
}

// generateAliyunArmsID generates a unique ID for the Aliyun ARMS alert
func generateAliyunArmsID(fingerprint, projectName string) string {
	return fmt.Sprintf("%s_%s_%d", projectName, fingerprint, time.Now().Unix())
}
