curl -X POST 'http://localhost:8080/webhook/aliyun_arms' \
-H 'Content-Type: application/json' \
-d '{
  "alerts": [
    {
      "annotations": {
        "_aliyun_arms_alert_value": "15.521240234375",
        "_aliyun_arms_alert_message": "null\n消息ID: ac10c42a16124966960554200d2450-7996494\n",
        "_aliyun_arms_alert_now_value": "15.521240234375",
        "message": "报警：命名空间: {{$labels.namespace}} / Pod: {{$labels.pod_name}} / 容器: {{$labels.container}} 内存使用率超>过80%, 当前值{{ printf \"%.2f\" $value }}%",
        "value": "15.521240234375",
        "_aliyun_arms_alert_past_value": "15.521240234375"
      },
      "endsAt": "2021-02-22T07:27:15.404000000Z",
      "fingerprint": "bec72890cc2c7b4a027e008df0cd1013",
      "labels": {
        "container": "kube-state-metrics",
        "severity": "warning",
        "_aliyun_arms_alert_level": "ERROR",
        "instance": "***********:10255",
        "clustername": "klyz1688-kubernetes-1",
        "_aliyun_arms_alert_type": "101",
        "_aliyun_arms_integration_name": "测试集成-prometheus",
        "alertname": "容器内存使用率大于80%",
        "_aliyun_arms_userid": "1131971649496228",
        "_aliyun_arms_involvedObject_name": "klyz1688-kubernetes-1",
        "pod_name": "kube-state-metrics-ccb59dbff-jljg4",
        "_aliyun_arms_involvedObject_id": "cb36dcafb9b9340498fad2e1f40b9a254",
        "_aliyun_arms_region_id": "cn-hangzhou",
        "_aliyun_arms_involvedObject_kind": "cluster",
        "_aliyun_arms_product_type": "PROMETHEUS",
        "name": "k8s_kube-state-metrics_kube-state-metrics-ccb59dbff-jljg4_arms-prom_359508f3-7e76-4740-b915-41ea48849641_0",
        "namespace": "arms-prom",
        "_aliyun_arms_integration_id": "80",
        "_aliyun_arms_involvedObject_type": "ManagedKubernetes",
        "_aliyun_arms_alert_rule_id": "3927051"
      },
      "startsAt": "2021-02-22T07:18:15.578000000Z",
      "status": "firing"
    }
  ],
  "commonAnnotations": {
    "_aliyun_arms_alert_value": "15.521240234375",
    "_aliyun_arms_alert_message": "null\n消息ID: ac10c42a16124966960554200d2450-7996494\n",
    "_aliyun_arms_alert_now_value": "15.521240234375",
    "message": "报警：命名空间: {{$labels.namespace}} / Pod: {{$labels.pod_name}} / 容器: {{$labels.container}} 内存使用率超过80%, 当前值{{ printf \"%.2f\" $value }}%",
    "value": "15.521240234375",
    "_aliyun_arms_alert_past_value": "15.521240234375"
  },
  "commonLabels": {
    "container": "kube-state-metrics",
    "severity": "warning",
    "_aliyun_arms_alert_level": "ERROR",
    "instance": "***********:10255",
    "clustername": "klyz1688-kubernetes-1",
    "_aliyun_arms_alert_type": "101",
    "_aliyun_arms_integration_name": "测试集成-prometheus",
    "alertname": "容器内存使用率大于80%",
    "_aliyun_arms_userid": "1131971649496228",
    "_aliyun_arms_involvedObject_name": "klyz1688-kubernetes-1",
    "pod_name": "kube-state-metrics-ccb59dbff-jljg4",
    "_aliyun_arms_involvedObject_id": "cb36dcafb9b9340498fad2e1f40b9a254",
    "_aliyun_arms_region_id": "cn-hangzhou",
    "_aliyun_arms_involvedObject_kind": "cluster",
    "_aliyun_arms_product_type": "PROMETHEUS",
    "name": "k8s_kube-state-metrics_kube-state-metrics-ccb59dbff-jljg4_arms-prom_359508f3-7e76-4740-b915-41ea48849641_0",
    "namespace": "arms-prom",
    "_aliyun_arms_integration_id": "80",
    "_aliyun_arms_involvedObject_type": "ManagedKubernetes",
    "_aliyun_arms_alert_rule_id": "3927051"
  },
  "externalURL": "https://alerts.console.aliyun.com/#/alarm/alert/detail/2848",
  "groupLabels": {
    "alertname": "容器内存使用率大于80%"
  },
  "receiver": "testjiubian",
  "status": "firing"
}'